import { Injectable, NotFoundException, BadRequestException, Inject } from '@nestjs/common';
import { InjectModel } from '@nestjs/mongoose';
import { Model, Types } from 'mongoose';
import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Cache } from 'cache-manager';
import { Sensor } from './sensor.interface';
import { CreateSensorDto } from './dto/create-sensor.dto';
import { UpdateSensorDto } from './dto/update-sensor.dto';
import Constants from 'src/common/constants';
import { AssignmentDto } from './dto/assign-sensor.dto';
import { SensorStatsDto } from './dto/sensor-stats.dto';

@Injectable()
export class SensorService {
  private static readonly CACHE_TTL_SENSORS = 300; // 5 minutes
  private static readonly CACHE_TTL_SENSOR_DETAIL = 600; // 10 minutes

  constructor(
    @InjectModel(Constants.sensorProfile) private sensorModel: Model<Sensor>,
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
  ) {}

  async create(sensorData: CreateSensorDto, userId: string): Promise<Sensor> {
    // Validate if NODE_ID already exists
    const existingSensor = await this.sensorModel.findOne({
      NODE_ID: sensorData.NODE_ID,
      isDeleted: false
    }).exec();

    if (existingSensor) {
      throw new BadRequestException(`Sensor with NODE_ID ${sensorData.NODE_ID} already exists`);
    }

    // Create new sensor
    const newSensor = new this.sensorModel({
      ...sensorData,
      TIME_STAMP: sensorData.TIME_STAMP || new Date(),
      first_seen: sensorData.first_seen || new Date(),
      createdBy: new Types.ObjectId(userId),
      updatedBy: new Types.ObjectId(userId),
    });

    const savedSensor = await newSensor.save();

    // Clear cache - we'll clear specific cache patterns
    if (process.env.ENABLE_CACHE === 'true') {
      // Clear common cache patterns that might be affected
      await this.cacheManager.del(`sensors:all:${sensorData.assignment?.org_id || 'all'}:1:10`);
      await this.cacheManager.del(`sensors:org:${sensorData.assignment?.org_id}`);
    }

    return savedSensor;
  }

  async assignSensorToOrg(assignmentDto: AssignmentDto, node_id: string): Promise<AssignmentDto> {
    const sensor = await this.sensorModel.findOne({
      NODE_ID: node_id
    }).exec();

    if (!sensor) {
      throw new NotFoundException(`Sensor with NODE_ID ${node_id} not found`);
    }

    const updatedSensor = await this.sensorModel.findByIdAndUpdate(
      sensor._id,
      {
        'assignment.org_id': assignmentDto.org_id,
        'assignment.auth0_id': assignmentDto.auth0_id,
        'assignment.assigned_at': new Date(),
        'assignment.notes': assignmentDto.notes,
      },
      { new: true }
    ).exec();

    return updatedSensor.assignment;
  }

  async findAll(page: number = 1, limit: number = 10): Promise<{ sensors: Sensor[], total: number, page: number, totalPages: number }> {
    const cacheKey = `sensors:all:${page}:${limit}`;
    
    // Check cache first
    if (process.env.ENABLE_CACHE === 'true') {
      const cached = await this.cacheManager.get(cacheKey);
      if (cached) {
        return cached as any;
      }
    }

    const skip = (page - 1) * limit;
    const filter: any = { };

    const [sensors, total] = await Promise.all([
      this.sensorModel.aggregate([
        { $match: filter },
        { $lookup: {
          from: 'organizations',
          localField: 'assignment.org_id',
          foreignField: '_id',
          as: 'organization'
        }
      },
      {
        $project: {
          NODE_ID: 1,
          HOST_NAME: 1,
          connectivity: 1,
          organization: 1,
          location: 1
        }
      },
        { $skip: skip },
        { $limit: limit },
      ]).exec(),
      this.sensorModel.countDocuments(filter).exec()
    ]);

    const result = {
      sensors,
      total,
      page,
      totalPages: Math.ceil(total / limit)
    };

    // Store in cache
    if (process.env.ENABLE_CACHE === 'true') {
      await this.cacheManager.set(cacheKey, result, SensorService.CACHE_TTL_SENSORS);
    }

    return result;
  }

  async findOne(id: string): Promise<Sensor> {
    const cacheKey = `sensor:${id}`;
    
    // Check cache first
    if (process.env.ENABLE_CACHE === 'true') {
      const cached = await this.cacheManager.get(cacheKey);
      if (cached) {
        return cached as Sensor;
      }
    }

    const sensor = await this.sensorModel.findOne({ 
      _id: id, 
      isDeleted: false 
    }).exec();

    if (!sensor) {
      throw new NotFoundException(`Sensor with ID ${id} not found`);
    }

    // Store in cache
    if (process.env.ENABLE_CACHE === 'true') {
      await this.cacheManager.set(cacheKey, sensor, SensorService.CACHE_TTL_SENSOR_DETAIL);
    }

    return sensor;
  }

  async findByNodeId(nodeId: string): Promise<Sensor> {
    const cacheKey = `sensor:node:${nodeId}`;
    
    // Check cache first
    if (process.env.ENABLE_CACHE === 'true') {
      const cached = await this.cacheManager.get(cacheKey);
      if (cached) {
        return cached as Sensor;
      }
    }

    const sensor = await this.sensorModel.findOne({ 
      NODE_ID: nodeId, 
      isDeleted: false 
    }).exec();

    if (!sensor) {
      throw new NotFoundException(`Sensor with NODE_ID ${nodeId} not found`);
    }

    // Store in cache
    if (process.env.ENABLE_CACHE === 'true') {
      await this.cacheManager.set(cacheKey, sensor, SensorService.CACHE_TTL_SENSOR_DETAIL);
    }

    return sensor;
  }

  async findByOrgId(orgId: string): Promise<Sensor[]> {
    const cacheKey = `sensors:org:${orgId}`;
    
    // Check cache first
    if (process.env.ENABLE_CACHE === 'true') {
      const cached = await this.cacheManager.get(cacheKey);
      if (cached) {
        return cached as Sensor[];
      }
    }

    const sensors = await this.sensorModel.find({
      'assignment.org_id': orgId,
      isDeleted: false
    }).sort({ TIME_STAMP: -1 }).exec();

    // Store in cache
    if (process.env.ENABLE_CACHE === 'true') {
      await this.cacheManager.set(cacheKey, sensors, SensorService.CACHE_TTL_SENSORS);
    }

    return sensors;
  }

  async update(id: string, sensorData: UpdateSensorDto, userId: string): Promise<Sensor> {
    // Check if sensor exists
    const sensor = await this.sensorModel.findOne({ _id: id, isDeleted: false }).exec();

    if (!sensor) {
      throw new NotFoundException(`Sensor with ID ${id} not found`);
    }

    // If NODE_ID is being updated, check if it's unique
    if (sensorData.NODE_ID && sensorData.NODE_ID !== sensor.NODE_ID) {
      const existingSensor = await this.sensorModel.findOne({
        NODE_ID: sensorData.NODE_ID,
        isDeleted: false,
        _id: { $ne: id }
      }).exec();

      if (existingSensor) {
        throw new BadRequestException(`Sensor with NODE_ID ${sensorData.NODE_ID} already exists`);
      }
    }

    // Update sensor
    const updatedSensor = await this.sensorModel.findByIdAndUpdate(
      id,
      {
        ...sensorData,
        updatedBy: new Types.ObjectId(userId),
        updatedAt: new Date(),
      },
      { new: true }
    ).exec();

    // Clear cache
    if (process.env.ENABLE_CACHE === 'true') {
      await this.cacheManager.del(`sensor:${id}`);
      if (sensor.NODE_ID) {
        await this.cacheManager.del(`sensor:node:${sensor.NODE_ID}`);
      }
      // Clear list caches that might be affected
      await this.cacheManager.del(`sensors:all:${sensor.assignment?.org_id || 'all'}:1:10`);
      await this.cacheManager.del(`sensors:org:${sensor.assignment?.org_id}`);
    }

    return updatedSensor;
  }

  async delete(id: string, userId: string): Promise<{ message: string }> {
    const sensor = await this.sensorModel.findOne({ _id: id, isDeleted: false }).exec();

    if (!sensor) {
      throw new NotFoundException(`Sensor with ID ${id} not found`);
    }

    // Soft delete
    await this.sensorModel.findByIdAndUpdate(
      id,
      {
        isDeleted: true,
        isActive: false,
        deletedAt: new Date(),
        deletedBy: new Types.ObjectId(userId),
      }
    ).exec();

    // Clear cache
    if (process.env.ENABLE_CACHE === 'true') {
      await this.cacheManager.del(`sensor:${id}`);
      if (sensor.NODE_ID) {
        await this.cacheManager.del(`sensor:node:${sensor.NODE_ID}`);
      }
      // Clear list caches that might be affected
      await this.cacheManager.del(`sensors:all:${sensor.assignment?.org_id || 'all'}:1:10`);
      await this.cacheManager.del(`sensors:org:${sensor.assignment?.org_id}`);
    }

    return { message: 'Sensor deleted successfully' };
  }

  async getSensorStats(): Promise<SensorStatsDto> {
    const cacheKey = 'sensor:stats';

    // Check cache first
    if (process.env.ENABLE_CACHE === 'true') {
      const cached = await this.cacheManager.get(cacheKey);
      if (cached) {
        return cached as { totalSensors: number, onlineSensors: number, offlineSensors: number };
      }
    }

    const stats = await this.sensorModel.aggregate([
      {
        $match: {
          isDeleted: { $ne: true }
        }
      },
      {
        $group: {
          _id: null,
          totalSensors: { $sum: 1 },
          onlineSensors: {
            $sum: {
              $cond: [{ $eq: ['$connectivity.state', 1] }, 1, 0]
            }
          },
          offlineSensors: {
            $sum: {
              $cond: [{ $ne: ['$connectivity.state', 1] }, 1, 0]
            }
          }
        }
      },
      {
        $project: {
          _id: 0,
          totalSensors: 1,
          onlineSensors: 1,
          offlineSensors: 1
        }
      }
    ]).exec();

    const result = stats[0] || {
      totalSensors: 0,
      onlineSensors: 0,
      offlineSensors: 0
    };

    // Store in cache
    if (process.env.ENABLE_CACHE === 'true') {
      await this.cacheManager.set(cacheKey, result, SensorService.CACHE_TTL_SENSORS);
    }

    return result;
  }

}
