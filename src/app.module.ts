import { <PERSON><PERSON><PERSON>, MiddlewareConsumer, Logger } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { DatabaseModule } from './database/database.module';
import { SpectrumDatabaseModule } from './database/spectrumDatabase.module';
import { UsersModule } from './domains/users/users.module';
import { ConfigModule } from '@nestjs/config';
import { AuthenticationMiddleware } from './common/middleware/authentication.middleware';
import { AlertZoneController } from './domains/alertZones/alertZone.controller';
import { AlertZoneModule } from './domains/alertZones/alertZone.module';
import { LoggingModule } from './domains/logging/logging.module';
import { LoggingController } from './domains/logging/logging.controller';
import { SpectrumEventModule } from './domains/spectrumEvents/spectrumEvent.module';
import { SpectrumEventController } from './domains/spectrumEvents/spectrumEvent.controller';
import { NotificationModule } from './domains/alertNotifications/notification.module';
import { ServiceZoneModule } from './domains/serviceZone/serviceZone.module';
import { PythonExecutionController } from './domains/pythonExecution/pythonExecution.controller';
import { PythonExecutionModule } from './domains/pythonExecution/pythonExecution.module';
import { UserPreferencesController } from './domains/userPreferences/userPreferences.controller';
import { ServiceZonesController } from './domains/serviceZone/serviceZone.controller';
import { RolesController } from './domains/roles/roles.controller';
import { H3Service } from './utils/h3.service';
import { SystemNotificationsController } from './domains/systemNotifications/systemNotification.controller';
import { SystemNotificationModule } from './domains/systemNotifications/systemNotification.module';
import { DroneModule } from './domains/drones/drone.module';
import { DroneController } from './domains/drones/drone.controller';
import { DroneAuthorizationModule } from './domains/droneAuthorizations/droneAuthorization.module';
import { DroneAuthorizationController } from './domains/droneAuthorizations/droneAuthorization.controller';
import { AdminModule } from './domains/admin/admin.module';
import { AdminController } from './domains/admin/admin.controller';
import { EventModule } from './domains/events/event.module';
import { DashboardModule } from './domains/dashboard/dashboard.module';
import { DashboardController } from './domains/dashboard/dashboard.controller';
import { SensorModule } from './domains/sensors/sensor.module';
import { SensorController } from './domains/sensors/sensor.controller';
import { CacheModule } from '@nestjs/cache-manager';
import { createKeyv } from '@keyv/redis';

import { CacheableMemory } from 'cacheable';
import { AuthorizationMiddleware } from './common/middleware/authorization.middleware';

@Module({
  imports: [
    SystemNotificationModule,
    NotificationModule,
    UsersModule,
    AlertZoneModule,
    DatabaseModule,
    SpectrumDatabaseModule,
    ServiceZoneModule,
    PythonExecutionModule,
    DroneModule,
    DroneAuthorizationModule,
    SensorModule,
    AdminModule,
    EventModule,
    DashboardModule,
    LoggingModule,
    SpectrumEventModule,
    ConfigModule.forRoot(),
    CacheModule.registerAsync({
      isGlobal: true,
      useFactory: async () => {
        const stores = [];

        // Always add memory store as fallback first
        const memoryStore = new CacheableMemory({
          ttl: '1h', // 1 hour default TTL
          lruSize: 1000, // LRU cache with max 1000 items
        });
        stores.push(memoryStore);

        // Try to add Redis store with timeout
        try {
          const redisStore = createKeyv({
            url: process.env.REDIS_HOST || "redis://localhost:6379",
          });

          // Test Redis connection with timeout
          const connectionTest = Promise.race([
            redisStore.get('test-connection'),
            new Promise((_, reject) =>
              setTimeout(() => reject(new Error('Connection timeout')), 5000)
            )
          ]);

          await connectionTest;
          stores.unshift(redisStore); // Add Redis as primary store (first in array)
          console.log('✅ Redis cache store connected successfully');
          console.log('🔄 Using multi-tier cache: Redis (primary) + Memory (fallback)');
        } catch (error) {
          console.warn('⚠️ Redis cache store failed to connect:', error.message);
          console.log('🧠 Using in-memory cache store only');
        }

        return {
          stores,
        };
      },
    }),
  ],
 controllers: [
    AppController,
    AlertZoneController,
    PythonExecutionController,
    UserPreferencesController,
    ServiceZonesController,
    RolesController,
    SystemNotificationsController,
    DroneController,
    DroneAuthorizationController,
    SensorController,
    AdminController,
    DashboardController,
    LoggingController,
    SpectrumEventController
  ],
  providers: [AppService, H3Service, Logger],
})
export class AppModule {
  configure(consumer: MiddlewareConsumer) {
    consumer.apply(AuthenticationMiddleware).forRoutes('*');
    consumer.apply(AuthorizationMiddleware).forRoutes('*');
  }
}
